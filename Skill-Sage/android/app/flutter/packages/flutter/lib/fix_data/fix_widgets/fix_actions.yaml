# Copyright 2014 The Flutter Authors. All rights reserved.
# Use of this source code is governed by a BSD-style license that can be
# found in the LICENSE file.

# For details regarding the *Flutter Fix* feature, see
# https://flutter.dev/to/flutter-fix

# Please add new fixes to the top of the file, separated by one blank line
# from other fixes. In a comment, include a link to the PR where the change
# requiring the fix was made.

# Every fix must be tested. See the flutter/packages/flutter/test_fixes/README.md
# file for instructions on testing these data driven fixes.

# For documentation about this file format, see
# https://dart.dev/go/data-driven-fixes.

# * Fixes in this file are for Actions from the Widgets library. *
#   For fixes to
#     * BuildContext: fix_build_context.yaml
#     * Element: fix_element.yaml
#     * ListWheelScrollView: fix_list_wheel_scroll_view.yaml
#     * Widgets (general): fix_widgets.yaml
version: 1
transforms:
  # Changes made in https://github.com/flutter/flutter/pull/68921.
  - title: "Migrate from 'nullOk'"
    date: 2021-01-27
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      method: 'invoke'
      inClass: 'Actions'
    oneOf:
      - if: "nullOk == 'true'"
        changes:
          - kind: 'rename'
            newName: 'maybeInvoke'
          - kind: 'removeParameter'
            name: 'nullOk'
      - if: "nullOk == 'false'"
        changes:
          - kind: 'removeParameter'
            name: 'nullOk'
    variables:
      nullOk:
        kind: 'fragment'
        value: 'arguments[nullOk]'

  # Changes made in https://github.com/flutter/flutter/pull/68921.
  - title: "Migrate from 'nullOk'"
    date: 2021-01-27
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      method: 'find'
      inClass: 'Actions'
    oneOf:
      - if: "nullOk == 'true'"
        changes:
          - kind: 'rename'
            newName: 'maybeFind'
          - kind: 'removeParameter'
            name: 'nullOk'
      - if: "nullOk == 'false'"
        changes:
          - kind: 'removeParameter'
            name: 'nullOk'
    variables:
      nullOk:
        kind: 'fragment'
        value: 'arguments[nullOk]'

  # Changes made in https://github.com/flutter/flutter/pull/68921.
  - title: "Migrate from 'nullOk'"
    date: 2021-01-27
    element:
      uris: [ 'widgets.dart', 'material.dart', 'cupertino.dart' ]
      method: 'handler'
      inClass: 'Actions'
    changes:
      - kind: 'removeParameter'
        name: 'nullOk'

# Before adding a new fix: read instructions at the top of this file.
