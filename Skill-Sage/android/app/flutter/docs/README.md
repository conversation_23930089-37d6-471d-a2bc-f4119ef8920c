This wiki is primarily aimed at engineers building or making contributions to Flutter.

If you are new to Flutter, then you will find more general information on the Flutter project, including tutorials and samples, on our website at [flutter.dev](https://flutter.dev). For specific information about Flutter's APIs, consider our API reference which can be found at the [api.flutter.dev](https://api.flutter.dev/).

If you want to know what we're likely to do in the future, our [roadmap](./roadmap/Roadmap.md) may be of interest.

If you intend to contribute to Flutter, welcome! You are encouraged to start with [our contributor guide](../CONTRIBUTING.md), which helps onboard new team members. It points to the most relevant pages on this wiki. You are also invited to join our [Discord](./contributing/Chat.md) server.


## Index of notable sections

* [Actionable bugs](./triage/README.md#what-makes-an-issue-actionable), and the closing of unactionable bugs
* [Breaking changes](./contributing/Tree-hygiene.md#handling-breaking-changes)
* [Cherrypick process](./releases/Flutter-Cherrypick-Process.md)
* [Closing issues](./contributing/issue_hygiene/README.md#closing-issues)
* [Dashboards](./infra/Dashboards.md)
* [Debugging a broken engine autoroll](./engine/Debugging-the-engine.md#bisecting-a-roll-failure)
* [Deprecations](./contributing/Tree-hygiene.md#deprecations)
* [Design documents](./contributing/Design-Documents.md)
* [Discord](./contributing/Chat.md)
* [Engineering Philosophy](./contributing/Style-guide-for-Flutter-repo.md#philosophy)
* [Flaky tests](./contributing/issue_hygiene/README.md#flaky-tests)
* [flutter.dev is down](./In-case-of-emergency.md)
* [Issue prioritization](./contributing/issue_hygiene/README.md#priorities)
* [Labels](./contributing/issue_hygiene/README.md#labels)
* [Milestones](./contributing/issue_hygiene/README.md#milestones)
* [Plugin compatibility policy](./contributing/Style-guide-for-Flutter-repo.md#plugin-compatibility)
* [Reviewing code](./contributing/Tree-hygiene.md#getting-a-code-review)
* [RFC process](./contributing/issue_hygiene/README.md#how-to-propose-a-specific-change)
* [Status of popular issues](./contributing/issue_hygiene/Popular-issues.md)
* [Style guide for Flutter repo](./contributing/Style-guide-for-Flutter-repo.md)
* [Submitting code, process for](./contributing/Tree-hygiene.md#overview)
* [Support levels, definitions of](./about/Values.md#support)
* [Symbolicating stack traces](./engine/Crashes.md)
* [Threading in the Engine](./about/The-Engine-architecture.md#threading)
* [When will my bug be fixed?](./contributing/issue_hygiene/README.md#when-will-my-bug-be-fixed)
* [Security best practices](./infra/Security.md#best-practices)
